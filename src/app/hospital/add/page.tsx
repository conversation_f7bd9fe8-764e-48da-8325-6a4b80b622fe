"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

interface HospitalFormData {
  name: string;
  logo: File | null;
  location: string;
  description: string;
  registrationNumber: string;
  isMainBranch: boolean;
  parentCompany: string;
  status: 'active' | 'inactive' | 'pending';
  bannerImages: File[];
}

const AddHospitalPage: React.FC = () => {
  const router = useRouter();
  
  const [formData, setFormData] = useState<HospitalFormData>({
    name: '',
    logo: null,
    location: '',
    description: '',
    registrationNumber: '',
    isMainBranch: false,
    parentCompany: '',
    status: 'active',
    bannerImages: []
  });

  const [errors, setErrors] = useState<Partial<Record<keyof HospitalFormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[name as keyof HospitalFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, logo: file }));
  };

  const handleBannerImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData(prev => ({ ...prev, bannerImages: files }));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof HospitalFormData, string>> = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Hospital name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Hospital name must be at least 3 characters';
    }

    // Location validation
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    } else if (formData.location.trim().length < 5) {
      newErrors.location = 'Location must be at least 5 characters';
    }

    // Description validation
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    // Registration number validation
    if (!formData.registrationNumber.trim()) {
      newErrors.registrationNumber = 'Registration number is required';
    } else if (!/^[A-Z0-9-]+$/.test(formData.registrationNumber)) {
      newErrors.registrationNumber = 'Registration number should contain only uppercase letters, numbers, and hyphens';
    }

    // Parent company validation for branch hospitals
    if (!formData.isMainBranch && !formData.parentCompany.trim()) {
      newErrors.parentCompany = 'Parent company is required for branch hospitals';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);
      try {
        // Here you would typically send the data to your backend API
        console.log('New hospital data:', formData);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Navigate back to hospital list after successful submission
        router.push('/hospital');
      } catch (error) {
        console.error('Error submitting hospital data:', error);
        // You could add error handling here
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleCancel = () => {
    router.push('/hospital');
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 flex flex-col max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleCancel}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Add New Hospital</h1>
              <p className="text-sm text-gray-600">Fill in the details below</p>
            </div>
          </div>

          {/* Action buttons in header */}
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Adding...
                </span>
              ) : (
                'Add Hospital'
              )}
            </button>
          </div>
        </div>

        {/* Form */}
        <div className="flex-1 bg-white shadow-sm rounded-lg border overflow-hidden">
          <div className="h-full p-6">
            <form onSubmit={handleSubmit} className="h-full">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                {/* Left Column */}
                <div className="space-y-4">
                  {/* Hospital Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Hospital Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.name ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter hospital name"
                    />
                    {errors.name && <p className="mt-1 text-xs text-red-600">{errors.name}</p>}
                  </div>

                  {/* Location */}
                  <div>
                    <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                      Location *
                    </label>
                    <input
                      type="text"
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.location ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter hospital location"
                    />
                    {errors.location && <p className="mt-1 text-xs text-red-600">{errors.location}</p>}
                  </div>

                  {/* Registration Number */}
                  <div>
                    <label htmlFor="registrationNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Registration Number *
                    </label>
                    <input
                      type="text"
                      id="registrationNumber"
                      name="registrationNumber"
                      value={formData.registrationNumber}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.registrationNumber ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter registration number"
                    />
                    {errors.registrationNumber && <p className="mt-1 text-xs text-red-600">{errors.registrationNumber}</p>}
                  </div>

                  {/* Status */}
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="pending">Pending</option>
                    </select>
                  </div>

                  {/* Is Main Branch */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isMainBranch"
                      name="isMainBranch"
                      checked={formData.isMainBranch}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isMainBranch" className="ml-2 block text-sm text-gray-900">
                      This is the main branch
                    </label>
                  </div>

                  {/* Parent Company (only show if not main branch) */}
                  {!formData.isMainBranch && (
                    <div>
                      <label htmlFor="parentCompany" className="block text-sm font-medium text-gray-700 mb-1">
                        Parent Company *
                      </label>
                      <input
                        type="text"
                        id="parentCompany"
                        name="parentCompany"
                        value={formData.parentCompany}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.parentCompany ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter parent company name"
                      />
                      {errors.parentCompany && <p className="mt-1 text-xs text-red-600">{errors.parentCompany}</p>}
                    </div>
                  )}
                </div>

                {/* Right Column */}
                <div className="space-y-4">
                  {/* Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Description *
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.description ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Enter hospital description"
                    />
                    {errors.description && <p className="mt-1 text-xs text-red-600">{errors.description}</p>}
                  </div>

                  {/* Logo Upload */}
                  <div>
                    <label htmlFor="logo" className="block text-sm font-medium text-gray-700 mb-1">
                      Hospital Logo
                    </label>
                    <input
                      type="file"
                      id="logo"
                      name="logo"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {formData.logo && (
                      <p className="mt-1 text-xs text-green-600">
                        ✓ Selected: {formData.logo.name}
                      </p>
                    )}
                  </div>

                  {/* Banner Images */}
                  <div>
                    <label htmlFor="bannerImages" className="block text-sm font-medium text-gray-700 mb-1">
                      Banner Images
                    </label>
                    <input
                      type="file"
                      id="bannerImages"
                      name="bannerImages"
                      accept="image/*"
                      multiple
                      onChange={handleBannerImagesChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">You can select multiple images</p>
                    {formData.bannerImages.length > 0 && (
                      <p className="mt-1 text-xs text-green-600">
                        ✓ Selected {formData.bannerImages.length} image(s)
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddHospitalPage;
